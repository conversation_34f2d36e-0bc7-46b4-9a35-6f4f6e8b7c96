package config

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// DingTalkConfig 钉钉配置结构
type DingTalkConfig struct {
	AppID        string `yaml:"app_id"`
	AgentID      string `yaml:"agent_id"`
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	ID           string `yaml:"id"`
	Name         string `yaml:"name"`
	FirstContact string `yaml:"first_contact"`
}

// GroupInfo 群组信息结构
type GroupInfo struct {
	ID           string `yaml:"id"`
	Name         string `yaml:"name"`
	FirstContact string `yaml:"first_contact"`
}

// IDManager ID管理结构
type IDManager struct {
	Users  []UserInfo  `yaml:"users"`
	Groups []GroupInfo `yaml:"groups"`
}

// MessageTemplates 消息模板结构
type MessageTemplates struct {
	Messages struct {
		Help           string `yaml:"help"`
		Status         string `yaml:"status"`
		UserListTitle  string `yaml:"user_list_title"`
		UserListItem   string `yaml:"user_list_item"`
		UserListEmpty  string `yaml:"user_list_empty"`
		GroupListTitle string `yaml:"group_list_title"`
		GroupListItem  string `yaml:"group_list_item"`
		GroupListEmpty string `yaml:"group_list_empty"`
		DefaultReply   string `yaml:"default_reply"`
		System         struct {
			NewUserRecorded     string `yaml:"new_user_recorded"`
			NewGroupRecorded    string `yaml:"new_group_recorded"`
			PrivateChatDetected string `yaml:"private_chat_detected"`
			IDFileUpdated       string `yaml:"id_file_updated"`
			IDFileSaveFailed    string `yaml:"id_file_save_failed"`
			BotStarting         string `yaml:"bot_starting"`
			BotStarted          string `yaml:"bot_started"`
			BotStopped          string `yaml:"bot_stopped"`
			BotStartFailed      string `yaml:"bot_start_failed"`
			ReplyFailed         string `yaml:"reply_failed"`
			ChatTypeUnknown     string `yaml:"chat_type_unknown"`
			MessageReceived     string `yaml:"message_received"`
			SenderID            string `yaml:"sender_id"`
			SenderNick          string `yaml:"sender_nick"`
			ConversationID      string `yaml:"conversation_id"`
			ConversationTitle   string `yaml:"conversation_title"`
			ConversationType    string `yaml:"conversation_type"`
			AtUsers             string `yaml:"at_users"`
			UnknownGroupName    string `yaml:"unknown_group_name"`
		} `yaml:"system"`
	} `yaml:"messages"`
	Commands struct {
		HelpKeywords      []string `yaml:"help_keywords"`
		StatusKeywords    []string `yaml:"status_keywords"`
		UserListKeywords  []string `yaml:"user_list_keywords"`
		GroupListKeywords []string `yaml:"group_list_keywords"`
	} `yaml:"commands"`
}

// LoadEnvConfig 从.env文件加载配置
func LoadEnvConfig(filename string) (*DingTalkConfig, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开配置文件 %s: %v", filename, err)
	}
	defer file.Close()

	config := &DingTalkConfig{}
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "app id":
			config.AppID = value
		case "AgentId":
			config.AgentID = value
		case "Client ID":
			config.ClientID = value
		case "Client Secret":
			config.ClientSecret = value
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取配置文件时出错: %v", err)
	}

	return config, nil
}

// LoadIDManager 从YAML文件加载ID管理器
func LoadIDManager(filename string) (*IDManager, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		// 如果文件不存在，创建一个空的管理器
		if os.IsNotExist(err) {
			return &IDManager{
				Users:  []UserInfo{},
				Groups: []GroupInfo{},
			}, nil
		}
		return nil, fmt.Errorf("无法读取ID管理文件 %s: %v", filename, err)
	}

	var manager IDManager
	if err := yaml.Unmarshal(data, &manager); err != nil {
		return nil, fmt.Errorf("解析ID管理文件失败: %v", err)
	}

	return &manager, nil
}

// SaveIDManager 保存ID管理器到YAML文件
func (m *IDManager) SaveIDManager(filename string) error {
	data, err := yaml.Marshal(m)
	if err != nil {
		return fmt.Errorf("序列化ID管理器失败: %v", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("保存ID管理文件失败: %v", err)
	}

	return nil
}

// AddUser 添加用户ID
func (m *IDManager) AddUser(userID, userName string) bool {
	// 检查用户是否已存在
	for _, user := range m.Users {
		if user.ID == userID {
			return false // 用户已存在
		}
	}

	// 添加新用户
	newUser := UserInfo{
		ID:           userID,
		Name:         userName,
		FirstContact: time.Now().Format("2006-01-02 15:04:05"),
	}
	m.Users = append(m.Users, newUser)
	return true // 添加成功
}

// AddGroup 添加群组ID
func (m *IDManager) AddGroup(groupID, groupName string) bool {
	// 检查群组是否已存在
	for _, group := range m.Groups {
		if group.ID == groupID {
			return false // 群组已存在
		}
	}

	// 添加新群组
	newGroup := GroupInfo{
		ID:           groupID,
		Name:         groupName,
		FirstContact: time.Now().Format("2006-01-02 15:04:05"),
	}
	m.Groups = append(m.Groups, newGroup)
	return true // 添加成功
}

// GetAllUserIDs 获取所有用户ID
func (m *IDManager) GetAllUserIDs() []string {
	var ids []string
	for _, user := range m.Users {
		ids = append(ids, user.ID)
	}
	return ids
}

// GetAllGroupIDs 获取所有群组ID
func (m *IDManager) GetAllGroupIDs() []string {
	var ids []string
	for _, group := range m.Groups {
		ids = append(ids, group.ID)
	}
	return ids
}

// LoadMessageTemplates 从YAML文件加载消息模板
func LoadMessageTemplates(filename string) (*MessageTemplates, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("无法读取模板文件 %s: %v", filename, err)
	}

	var templates MessageTemplates
	if err := yaml.Unmarshal(data, &templates); err != nil {
		return nil, fmt.Errorf("解析模板文件失败: %v", err)
	}

	return &templates, nil
}
