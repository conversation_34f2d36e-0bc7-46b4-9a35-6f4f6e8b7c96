# 钉钉机器人模板配置说明

## 概述

本次更新将钉钉机器人的**所有消息内容**（包括用户回复、系统日志、调试信息、命令关键词等）从硬编码完全改为配置文件管理，实现了100%的消息模板化，大大提高了系统的可维护性和灵活性。

## 主要变更

### 1. 新增文件

- `templates.yaml` - 消息模板配置文件
- `TEMPLATE_CONFIG.md` - 本说明文档

### 2. 修改文件

- `config/config.go` - 添加了 `MessageTemplates` 结构体和 `LoadMessageTemplates` 函数
- `bot/bot.go` - 重构所有消息生成函数，使用模板替代硬编码
- `main.go` - 添加模板加载逻辑

## 配置文件结构

### templates.yaml

```yaml
messages:
  # 基本回复模板
  help: "帮助信息内容"
  status: "状态信息模板，支持格式化参数 %d %s"
  default_reply: "默认回复模板"
  
  # 用户列表相关模板
  user_list_title: "用户列表标题"
  user_list_item: "用户列表项模板"
  user_list_empty: "用户列表为空时的提示"
  
  # 群组列表相关模板
  group_list_title: "群组列表标题"
  group_list_item: "群组列表项模板"
  group_list_empty: "群组列表为空时的提示"
  
  # 系统消息模板
  system:
    new_user_recorded: "新用户记录消息"
    new_group_recorded: "新群组记录消息"
    private_chat_detected: "私聊检测消息"
    id_file_updated: "ID文件更新消息"

    # 调试日志模板
    message_received: "收到消息日志模板"
    sender_id: "发送者ID日志模板"
    sender_nick: "发送者昵称日志模板"
    conversation_id: "会话ID日志模板"
    conversation_title: "会话标题日志模板"
    conversation_type: "会话类型日志模板"
    at_users: "@用户列表日志模板"

    # 默认值模板
    unknown_group_name: "未知群组默认名称"

# 命令关键词配置
commands:
  help_keywords: ["帮助", "help"]
  status_keywords: ["状态", "status"]
  user_list_keywords: ["用户列表"]
  group_list_keywords: ["群组列表"]
```

## 使用方法

### 1. 自定义消息模板

编辑 `templates.yaml` 文件，修改相应的消息模板：

```yaml
messages:
  help: |
    🤖 我的自定义机器人
    
    可用功能：
    • 帮助 - 显示帮助
    • 状态 - 查看状态
    
    联系管理员：<EMAIL>
```

### 2. 支持的格式化参数

某些模板支持格式化参数，使用 Go 的 `fmt.Sprintf` 语法：

- `status` 模板：`%d` (用户数), `%d` (群组数), `%s` (AppID), `%s` (AgentID), `%s` (ClientID)
- `user_list_item` 模板：`%d` (序号), `%s` (用户名), `%s` (用户ID), `%s` (首次联系时间)
- `group_list_item` 模板：`%d` (序号), `%s` (群组名), `%s` (群组ID), `%s` (首次联系时间)

### 3. 多行文本支持

使用 YAML 的多行文本语法：

```yaml
messages:
  help: |
    第一行
    第二行
    第三行
```

或者：

```yaml
messages:
  help: >
    这是一段很长的文本，
    会被合并成一行，
    适合长段落。
```

## 优势

### 1. 完全模板化
- **100%消息模板化**：bot.go中不再有任何硬编码的中文消息
- **包含调试日志**：连系统调试日志都可以通过配置文件自定义
- **命令关键词可配置**：用户触发命令的关键词也可以自定义

### 2. 易于维护
- 所有消息模板集中在一个配置文件中
- 无需重新编译即可修改消息内容
- 支持版本控制和团队协作

### 2. 国际化支持
- 可以轻松创建不同语言的模板文件
- 通过修改加载的模板文件实现多语言支持

### 3. 环境差异化
- 开发、测试、生产环境可以使用不同的模板
- 便于A/B测试和消息优化

### 4. 格式一致性
- 统一的消息格式和风格
- 减少硬编码带来的不一致问题

## 注意事项

### 1. 文件路径
- 确保 `templates.yaml` 文件在程序运行目录下
- 如需修改路径，请在 `main.go` 中调整 `LoadMessageTemplates` 的参数

### 2. 格式化参数
- 使用格式化参数的模板必须保持参数数量和类型一致
- 错误的格式化参数会导致运行时错误

### 3. YAML 语法
- 注意 YAML 的缩进和语法规则
- 特殊字符需要适当转义
- 建议使用 YAML 验证工具检查语法

### 4. 热重载
- 当前版本不支持热重载，修改模板后需要重启程序
- 未来版本可以考虑添加配置文件监控和热重载功能

## 示例配置

参考项目根目录下的 `templates.yaml` 文件，其中包含了完整的默认配置示例。

## 故障排除

### 1. 模板加载失败
- 检查 `templates.yaml` 文件是否存在
- 验证 YAML 语法是否正确
- 确认文件编码为 UTF-8

### 2. 格式化错误
- 检查模板中的 `%d` 和 `%s` 参数是否与代码中的参数匹配
- 确认参数顺序正确

### 3. 消息显示异常
- 检查模板内容是否包含特殊字符
- 验证多行文本的 YAML 语法是否正确
