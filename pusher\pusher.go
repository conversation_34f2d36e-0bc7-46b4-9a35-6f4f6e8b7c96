package pusher

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"shopifyordergo/config"
)

// MessagePusher 消息推送器
type MessagePusher struct {
	Config    *config.DingTalkConfig
	IDManager *config.IDManager
}

// NewMessagePusher 创建新的消息推送器
func NewMessagePusher(cfg *config.DingTalkConfig, idManager *config.IDManager) (*MessagePusher, error) {
	return &MessagePusher{
		Config:    cfg,
		IDManager: idManager,
	}, nil
}

// TextMessage 文本消息结构
type TextMessage struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
	At struct {
		AtUserIds []string `json:"atUserIds,omitempty"`
		AtMobiles []string `json:"atMobiles,omitempty"`
		IsAtAll   bool     `json:"isAtAll,omitempty"`
	} `json:"at,omitempty"`
}

// MarkdownMessage Markdown消息结构
type MarkdownMessage struct {
	MsgType  string `json:"msgtype"`
	Markdown struct {
		Title string `json:"title"`
		Text  string `json:"text"`
	} `json:"markdown"`
	At struct {
		AtUserIds []string `json:"atUserIds,omitempty"`
		AtMobiles []string `json:"atMobiles,omitempty"`
		IsAtAll   bool     `json:"isAtAll,omitempty"`
	} `json:"at,omitempty"`
}

// SendTextToUser 向指定用户发送文本消息
func (p *MessagePusher) SendTextToUser(userID, message string) error {
	return p.sendMessageToUser(userID, message)
}

// SendMarkdownToUser 向指定用户发送Markdown消息
func (p *MessagePusher) SendMarkdownToUser(userID, title, message string) error {
	// 对于API调用，Markdown消息也使用文本格式
	markdownContent := fmt.Sprintf("**%s**\n\n%s", title, message)
	return p.sendMessageToUser(userID, markdownContent)
}

// SendTextToGroup 向指定群组发送文本消息
func (p *MessagePusher) SendTextToGroup(groupID, message string, atUsers []string, atAll bool) error {
	// 对于API调用，暂时忽略@功能，直接发送文本消息
	// TODO: 后续可以实现@功能的API格式
	return p.sendMessageToGroup(groupID, message)
}

// SendMarkdownToGroup 向指定群组发送Markdown消息
func (p *MessagePusher) SendMarkdownToGroup(groupID, title, message string, atUsers []string, atAll bool) error {
	// 对于API调用，Markdown消息也使用文本格式
	markdownContent := fmt.Sprintf("**%s**\n\n%s", title, message)
	return p.sendMessageToGroup(groupID, markdownContent)
}

// BroadcastTextToAllUsers 向所有已记录用户广播文本消息
func (p *MessagePusher) BroadcastTextToAllUsers(message string) error {
	userIDs := p.IDManager.GetAllUserIDs()
	if len(userIDs) == 0 {
		return fmt.Errorf("没有已记录的用户ID")
	}

	var errors []string
	for _, userID := range userIDs {
		if err := p.SendTextToUser(userID, message); err != nil {
			errors = append(errors, fmt.Sprintf("发送给用户 %s 失败: %v", userID, err))
			log.Printf("发送消息给用户 %s 失败: %v", userID, err)
		} else {
			log.Printf("成功发送消息给用户: %s", userID)
		}
		// 添加延迟避免频率限制
		time.Sleep(100 * time.Millisecond)
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// BroadcastTextToAllGroups 向所有已记录群组广播文本消息
func (p *MessagePusher) BroadcastTextToAllGroups(message string, atAll bool) error {
	groupIDs := p.IDManager.GetAllGroupIDs()
	if len(groupIDs) == 0 {
		return fmt.Errorf("没有已记录的群组ID")
	}

	var errors []string
	for _, groupID := range groupIDs {
		if err := p.SendTextToGroup(groupID, message, nil, atAll); err != nil {
			errors = append(errors, fmt.Sprintf("发送给群组 %s 失败: %v", groupID, err))
			log.Printf("发送消息给群组 %s 失败: %v", groupID, err)
		} else {
			log.Printf("成功发送消息给群组: %s", groupID)
		}
		// 添加延迟避免频率限制
		time.Sleep(100 * time.Millisecond)
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// sendMessageToUser 向用户发送消息的通用方法
func (p *MessagePusher) sendMessageToUser(userID string, message string) error {
	// 获取访问令牌
	accessToken, err := p.GetAccessToken()
	if err != nil {
		return fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 构造请求URL
	url := "https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend"

	// 构造消息参数
	msgParam := map[string]string{
		"content": message,
	}
	msgParamJSON, err := json.Marshal(msgParam)
	if err != nil {
		return fmt.Errorf("序列化消息参数失败: %v", err)
	}

	// 构造请求体
	requestBody := map[string]interface{}{
		"robotCode": p.Config.ClientID, // 使用ClientID作为机器人代码
		"userIds":   []string{userID},
		"msgKey":    "sampleText",
		"msgParam":  string(msgParamJSON),
	}

	// 序列化请求体
	requestJSON, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-acs-dingtalk-access-token", accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != 200 {
		return fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 检查业务状态 - 钉钉API成功响应包含processQueryKey
	if _, exists := result["processQueryKey"]; !exists {
		// 如果没有processQueryKey，打印完整响应用于调试
		log.Printf("API响应异常，完整响应: %s", string(respBody))
		return fmt.Errorf("发送消息失败: 响应中缺少processQueryKey")
	}

	// 检查是否有无效的用户ID
	if invalidList, exists := result["invalidStaffIdList"]; exists {
		if list, ok := invalidList.([]interface{}); ok && len(list) > 0 {
			log.Printf("发现无效用户ID: %v", list)
			return fmt.Errorf("发送消息失败: 用户ID无效 %v", list)
		}
	}

	// 检查是否有被流控的用户ID
	if flowControlList, exists := result["flowControlledStaffIdList"]; exists {
		if list, ok := flowControlList.([]interface{}); ok && len(list) > 0 {
			log.Printf("发现被流控的用户ID: %v", list)
			// 流控不算失败，只是警告
			log.Printf("警告: 用户 %v 被流控，消息可能延迟发送", list)
		}
	}

	log.Printf("成功向用户 %s 发送消息", userID)
	return nil
}

// sendMessageToGroup 向群组发送消息的通用方法
func (p *MessagePusher) sendMessageToGroup(groupID string, message string) error {
	// 获取访问令牌
	accessToken, err := p.GetAccessToken()
	if err != nil {
		return fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 构造请求URL
	url := "https://api.dingtalk.com/v1.0/robot/groupMessages/send"

	// 构造消息参数
	msgParam := map[string]string{
		"content": message,
	}
	msgParamJSON, err := json.Marshal(msgParam)
	if err != nil {
		return fmt.Errorf("序列化消息参数失败: %v", err)
	}

	// 构造请求体
	requestBody := map[string]interface{}{
		"robotCode":          p.Config.ClientID, // 使用ClientID作为机器人代码
		"openConversationId": groupID,
		"msgKey":             "sampleText",
		"msgParam":           string(msgParamJSON),
	}

	// 序列化请求体
	requestJSON, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-acs-dingtalk-access-token", accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != 200 {
		return fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 检查业务状态 - 钉钉API成功响应包含processQueryKey
	if _, exists := result["processQueryKey"]; !exists {
		// 如果没有processQueryKey，打印完整响应用于调试
		log.Printf("群组API响应异常，完整响应: %s", string(respBody))
		return fmt.Errorf("发送消息失败: 响应中缺少processQueryKey")
	}

	// 对于群组消息，检查是否有其他错误信息
	log.Printf("群组消息发送成功，processQueryKey: %v", result["processQueryKey"])

	log.Printf("成功向群组 %s 发送消息", groupID)
	return nil
}

// GetAccessToken 获取访问令牌
func (p *MessagePusher) GetAccessToken() (string, error) {
	// 构造请求URL
	url := fmt.Sprintf("https://oapi.dingtalk.com/gettoken?appkey=%s&appsecret=%s",
		p.Config.ClientID, p.Config.ClientSecret)

	// 发送HTTP请求
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("获取访问令牌失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var result struct {
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("解析访问令牌响应失败: %v", err)
	}

	if result.ErrCode != 0 {
		return "", fmt.Errorf("获取访问令牌失败: %s", result.ErrMsg)
	}

	return result.AccessToken, nil
}
