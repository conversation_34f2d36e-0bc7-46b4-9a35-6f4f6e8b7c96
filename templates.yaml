# 钉钉机器人消息模板配置文件
messages:
  # 帮助信息模板
  help: |
    🤖 钉钉机器人帮助信息
    
    可用命令：
    • 帮助/help - 显示此帮助信息
    • 状态/status - 显示机器人状态
    • 用户列表 - 显示已记录的用户ID
    • 群组列表 - 显示已记录的群组ID
    
    机器人功能：
    ✅ 自动记录用户ID和群组ID
    ✅ 支持@机器人对话
    ✅ 支持主动推送消息（需要先与机器人对话）
    
    使用说明：
    1. 在群组中@机器人或私聊机器人
    2. 机器人会自动记录您的ID
    3. 管理员可以使用记录的ID主动推送消息

  # 状态信息模板
  status: |
    🔍 机器人状态信息
    
    📊 统计数据：
    • 已记录用户数: %d
    • 已记录群组数: %d
    
    ⚙️ 配置信息：
    • App ID: %s
    • Agent ID: %s
    • Client ID: %s
    
    ✅ 机器人运行正常

  # 用户列表标题模板
  user_list_title: "👥 已记录用户列表：\n\n"
  
  # 用户列表项模板
  user_list_item: |
    %d. %s
       ID: %s
       首次联系: %s

  # 用户列表为空模板
  user_list_empty: "📝 暂无已记录的用户"

  # 群组列表标题模板
  group_list_title: "🏢 已记录群组列表：\n\n"
  
  # 群组列表项模板
  group_list_item: |
    %d. %s
       ID: %s
       首次联系: %s

  # 群组列表为空模板
  group_list_empty: "📝 暂无已记录的群组"

  # 默认回复模板
  default_reply: "收到消息: %s\n\n发送 '帮助' 查看可用命令"

  # 系统消息模板
  system:
    new_user_recorded: "新用户ID已记录: %s (%s)"
    new_group_recorded: "新群组ID已记录: %s (%s)"
    private_chat_detected: "私聊消息，不记录群组ID"
    id_file_updated: "ID管理文件已更新"
    id_file_save_failed: "保存ID管理文件失败: %v"
    bot_starting: "正在启动钉钉机器人..."
    bot_started: "钉钉机器人启动成功！"
    bot_stopped: "钉钉机器人已停止"
    bot_start_failed: "启动机器人失败: %v"
    reply_failed: "回复消息失败: %v"
    chat_type_unknown: "无法确定聊天类型，ConversationType: %s, ConversationTitle: %s, ConversationId: %s"

    # 调试日志模板
    message_received: "收到消息: %s"
    sender_id: "发送者ID: %s"
    sender_nick: "发送者昵称: %s"
    conversation_id: "会话ID: %s"
    conversation_title: "会话标题: %s"
    conversation_type: "会话类型: %s"
    at_users: "@用户列表: %v"

    # 默认值模板
    unknown_group_name: "未知群组"

# 命令关键词配置
commands:
  help_keywords: ["帮助", "help"]
  status_keywords: ["状态", "status"]
  user_list_keywords: ["用户列表"]
  group_list_keywords: ["群组列表"]
