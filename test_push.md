# 钉钉机器人推送消息测试指南

## 功能说明

现在你的钉钉助手插件已经支持主动推送消息功能！我已经实现了以下功能：

### ✅ 已实现的功能

1. **机器人发送单聊消息** - 向指定用户发送消息
2. **机器人发送群聊消息** - 向指定群组发送消息
3. **批量广播消息** - 向所有已记录的用户/群组广播消息
4. **测试推送功能** - 快速测试消息推送是否正常工作

### 🔧 技术实现

- 使用钉钉官方API：
  - 单聊消息：`POST https://api.dingtalk.com/v1.0/robot/otoMessages/send`
  - 群聊消息：`POST https://api.dingtalk.com/v1.0/robot/groupMessages/send`
- 自动获取access_token
- 完整的错误处理和日志记录
- 支持文本消息和Markdown消息

## 测试步骤

### 1. 启动程序
```bash
go run main.go
# 或者
./shopifyordergo.exe
```

### 2. 查看已记录的ID
```bash
> users    # 查看已记录的用户
> groups   # 查看已记录的群组
```

### 3. 测试推送功能

#### 快速测试（推荐）
```bash
# 向第一个已记录用户发送测试消息
> test user

# 向第一个已记录群组发送测试消息  
> test group

# 向指定用户发送测试消息
> test user 024326423122739246

# 向指定群组发送测试消息
> test group cid7notLFWYHlJlxDwYAogb2Q==
```

#### 手动发送消息
```bash
# 向用户发送消息
> send user 024326423122739246 你好，这是一条测试消息！

# 向群组发送消息
> send group cid7notLFWYHlJlxDwYAogb2Q== 大家好，机器人推送功能已上线！

# 向所有用户广播
> broadcast users 系统维护通知：今晚22:00-24:00进行系统维护

# 向所有群组广播
> broadcast groups 重要公告：新功能已上线
```

### 4. 查看帮助
```bash
> help
```

## 当前配置

根据你的 `dingdingid.yaml` 文件，当前已记录：

- **用户**: 024326423122739246 (JH(姜昊))
- **群组**: cid7notLFWYHlJlxDwYAogb2Q== (JH(姜昊),xiaoYuan（袁可）)

## 预期结果

如果配置正确，你应该能看到：

1. **成功情况**：
   ```
   > test user
   使用第一个已记录的用户ID: 024326423122739246 (JH(姜昊))
   正在向用户 024326423122739246 发送测试消息...
   ✅ 测试消息发送成功！
   ```

2. **失败情况**（可能的原因）：
   - ❌ 获取访问令牌失败 - 检查ClientID和ClientSecret
   - ❌ API请求失败 - 检查网络连接和API权限
   - ❌ 发送消息失败 - 检查机器人权限和用户/群组ID

## 故障排除

### 常见问题

1. **获取访问令牌失败**
   - 检查 `.env` 文件中的 `Client ID` 和 `Client Secret` 是否正确
   - 确保机器人应用已正确配置

2. **API请求失败**
   - 确保网络连接正常
   - 检查机器人是否有发送消息的权限
   - 确认用户ID和群组ID格式正确

3. **用户收不到消息**
   - 确保用户之前与机器人有过对话（这是钉钉的限制）
   - 检查用户ID是否正确

4. **群组收不到消息**
   - 确保机器人已被添加到群组
   - 确保群组ID格式正确（以 `cid` 开头）

### 调试建议

1. 先使用 `test` 命令进行快速测试
2. 查看控制台日志输出，了解详细错误信息
3. 确认机器人在钉钉开发者后台的配置正确
4. 如果仍有问题，可以检查钉钉开发者文档的最新API变更

## 下一步

现在你可以：
1. 测试推送功能是否正常工作
2. 根据需要调整消息内容和格式
3. 集成到你的业务流程中
4. 添加更多自定义功能

祝测试顺利！🎉
