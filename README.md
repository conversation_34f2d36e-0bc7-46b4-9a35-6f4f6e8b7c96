# 钉钉机器人 - ShopifyOrderGo

这是一个基于Go语言开发的钉钉机器人，支持Stream模式的双向通信功能。

## 功能特性

### 🤖 机器人功能
- ✅ Stream模式消息接收
- ✅ 自动回复@机器人的消息
- ✅ 自动记录用户ID和群组ID
- ✅ 主动推送消息到用户和群组
- ✅ 支持文本和Markdown格式消息
- ✅ 交互式命令行界面

### 📋 支持的命令
**机器人对话命令：**
- `帮助` / `help` - 显示帮助信息
- `状态` / `status` - 显示机器人状态
- `用户列表` - 显示已记录的用户
- `群组列表` - 显示已记录的群组

**命令行界面命令：**
- `help` - 显示帮助信息
- `status` - 显示机器人状态
- `users` - 显示用户列表
- `groups` - 显示群组列表
- `send user <userID> <message>` - 向指定用户发送消息
- `send group <groupID> <message>` - 向指定群组发送消息
- `broadcast users <message>` - 向所有用户广播消息
- `broadcast groups <message>` - 向所有群组广播消息

## 项目结构

```
shopifyordergo/
├── main.go              # 主程序入口
├── .env                 # 环境配置文件
├── dingdingid.yaml      # ID管理文件
├── go.mod               # Go模块文件
├── go.sum               # 依赖校验文件
├── config/
│   └── config.go        # 配置管理模块
├── bot/
│   └── bot.go           # 机器人核心功能
└── pusher/
    └── pusher.go        # 消息推送模块
```

## 安装和配置

### 1. 环境要求
- Go 1.19 或更高版本
- 钉钉开发者账号
- 已创建的钉钉机器人应用

### 2. 配置文件
确保 `.env` 文件包含以下配置：
```
app id:你的应用ID
AgentId:你的AgentID
Client ID:你的ClientID
Client Secret:你的ClientSecret
```

### 3. 安装依赖
```bash
go mod tidy
```

### 4. 运行程序
```bash
go run main.go
```

## 使用说明

### 首次使用
1. 启动机器人程序
2. 将机器人添加到钉钉群组或与机器人私聊
3. @机器人或发送消息，机器人会自动记录您的ID
4. 使用命令行界面向已记录的用户/群组推送消息

### ID管理
- 机器人会自动将与其对话的用户ID和群组ID保存到 `dingdingid.yaml` 文件
- 只有与机器人对话过的用户/群组才能接收主动推送的消息
- 可以手动编辑 `dingdingid.yaml` 文件来管理ID列表

### 消息推送示例
```bash
# 向特定用户发送消息
> send user 123456789 Hello World

# 向特定群组发送消息  
> send group cid123456789 群组通知消息

# 向所有用户广播消息
> broadcast users 系统维护通知

# 向所有群组广播消息
> broadcast groups 重要公告
```

## 开发说明

### 核心模块

**config/config.go**
- 配置文件解析
- ID管理功能
- YAML文件读写

**bot/bot.go**
- Stream模式消息接收
- 消息处理和回复
- ID自动记录

**pusher/pusher.go**
- 主动消息推送
- 支持文本和Markdown格式
- 批量广播功能

### 扩展功能
可以根据需要扩展以下功能：
- 定时任务推送
- 消息模板管理
- 用户权限管理
- 消息统计分析
- 与其他系统集成

## 注意事项

1. **权限要求**：机器人需要相应的权限才能发送消息
2. **频率限制**：注意钉钉API的调用频率限制
3. **ID记录**：用户必须先与机器人对话才能接收主动推送
4. **安全性**：妥善保管配置文件中的敏感信息

## 故障排除

### 常见问题
1. **连接失败**：检查Client ID和Client Secret是否正确
2. **消息发送失败**：确认用户/群组ID是否正确且已记录
3. **权限错误**：检查机器人是否有相应的发送权限

### 日志查看
程序运行时会输出详细的日志信息，包括：
- 连接状态
- 消息接收情况
- ID记录情况
- 错误信息

## 技术支持

如有问题，请检查：
1. 钉钉开发者文档
2. 程序日志输出
3. 配置文件格式

## 许可证

本项目采用 MIT 许可证。
