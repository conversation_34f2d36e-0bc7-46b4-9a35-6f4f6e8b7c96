package bot

import (
	"context"
	"fmt"
	"log"
	"strings"

	"shopifyordergo/config"

	"github.com/open-dingtalk/dingtalk-stream-sdk-go/chatbot"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/client"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/logger"
)

// DingTalkBot 钉钉机器人结构
type DingTalkBot struct {
	Config    *config.DingTalkConfig
	IDManager *config.IDManager
	Templates *config.MessageTemplates
	Client    *client.StreamClient
}

// NewDingTalkBot 创建新的钉钉机器人实例
func NewDingTalkBot(cfg *config.DingTalkConfig, idManager *config.IDManager, templates *config.MessageTemplates) *DingTalkBot {
	return &DingTalkBot{
		Config:    cfg,
		IDManager: idManager,
		Templates: templates,
	}
}

// OnChatBotMessageReceived 处理接收到的机器人消息
func (bot *DingTalkBot) OnChatBotMessageReceived(ctx context.Context, data *chatbot.BotCallbackDataModel) ([]byte, error) {
	log.Printf("收到消息: %s", data.Text.Content)
	log.Printf("发送者ID: %s", data.SenderStaffId)
	log.Printf("发送者昵称: %s", data.SenderNick)
	log.Printf("会话ID: %s", data.ConversationId)
	log.Printf("会话标题: %s", data.ConversationTitle)
	log.Printf("会话类型: %s", data.ConversationType)
	log.Printf("@用户列表: %v", data.AtUsers)

	// 记录用户ID和群组ID
	bot.recordIDs(data)

	// 处理消息内容
	content := strings.TrimSpace(data.Text.Content)
	response := bot.processMessage(content, data)

	// 回复消息
	replier := chatbot.NewChatbotReplier()
	if err := replier.SimpleReplyText(ctx, data.SessionWebhook, []byte(response)); err != nil {
		log.Printf(bot.Templates.Messages.System.ReplyFailed, err)
		return nil, err
	}

	return []byte(""), nil
}

// recordIDs 记录用户ID和群组ID
func (bot *DingTalkBot) recordIDs(data *chatbot.BotCallbackDataModel) {
	var updated bool

	// 记录用户ID
	if data.SenderStaffId != "" {
		if bot.IDManager.AddUser(data.SenderStaffId, data.SenderNick) {
			log.Printf(bot.Templates.Messages.System.NewUserRecorded, data.SenderStaffId, data.SenderNick)
			updated = true
		}
	}

	// 判断是否为群聊，只有群聊才记录群组ID
	if bot.isGroupChat(data) {
		groupName := "未知群组"
		if data.ConversationTitle != "" {
			groupName = data.ConversationTitle
		}
		if bot.IDManager.AddGroup(data.ConversationId, groupName) {
			log.Printf(bot.Templates.Messages.System.NewGroupRecorded, data.ConversationId, groupName)
			updated = true
		}
	} else {
		log.Printf(bot.Templates.Messages.System.PrivateChatDetected)
	}

	// 如果有更新，保存到文件
	if updated {
		if err := bot.IDManager.SaveIDManager("dingdingid.yaml"); err != nil {
			log.Printf(bot.Templates.Messages.System.IDFileSaveFailed, err)
		} else {
			log.Println(bot.Templates.Messages.System.IDFileUpdated)
		}
	}
}

// isGroupChat 判断是否为群聊
func (bot *DingTalkBot) isGroupChat(data *chatbot.BotCallbackDataModel) bool {
	// 方法1: 检查会话类型
	if data.ConversationType == "2" {
		return true // 群聊
	}
	if data.ConversationType == "1" {
		return false // 私聊
	}

	// 方法2: 检查会话标题（群聊通常有标题，私聊没有）
	if data.ConversationTitle != "" {
		return true
	}

	// 方法3: 检查ConversationId格式（这是备用方法，不太可靠）
	// 群聊ID通常以特定格式开头，但这个规则可能会变化
	// 记录调试信息以便分析
	log.Printf(bot.Templates.Messages.System.ChatTypeUnknown,
		data.ConversationType, data.ConversationTitle, data.ConversationId)

	// 如果以上方法都无法确定，默认认为是私聊（更安全的做法）
	return false
}

// processMessage 处理消息内容并生成回复
func (bot *DingTalkBot) processMessage(content string, data *chatbot.BotCallbackDataModel) string {
	// 移除@机器人的部分
	content = strings.TrimSpace(content)

	// 基本命令处理
	switch {
	case strings.Contains(content, "帮助") || strings.Contains(content, "help"):
		return bot.getHelpMessage()
	case strings.Contains(content, "状态") || strings.Contains(content, "status"):
		return bot.getStatusMessage()
	case strings.Contains(content, "用户列表"):
		return bot.getUserListMessage()
	case strings.Contains(content, "群组列表"):
		return bot.getGroupListMessage()
	default:
		return fmt.Sprintf(bot.Templates.Messages.DefaultReply, content)
	}
}

// getHelpMessage 获取帮助信息
func (bot *DingTalkBot) getHelpMessage() string {
	return bot.Templates.Messages.Help
}

// getStatusMessage 获取状态信息
func (bot *DingTalkBot) getStatusMessage() string {
	userCount := len(bot.IDManager.Users)
	groupCount := len(bot.IDManager.Groups)

	return fmt.Sprintf(bot.Templates.Messages.Status, userCount, groupCount, bot.Config.AppID, bot.Config.AgentID, bot.Config.ClientID)
}

// getUserListMessage 获取用户列表信息
func (bot *DingTalkBot) getUserListMessage() string {
	if len(bot.IDManager.Users) == 0 {
		return bot.Templates.Messages.UserListEmpty
	}

	var result strings.Builder
	result.WriteString(bot.Templates.Messages.UserListTitle)

	for i, user := range bot.IDManager.Users {
		result.WriteString(fmt.Sprintf(bot.Templates.Messages.UserListItem, i+1, user.Name, user.ID, user.FirstContact))
		result.WriteString("\n\n")
	}

	return result.String()
}

// getGroupListMessage 获取群组列表信息
func (bot *DingTalkBot) getGroupListMessage() string {
	if len(bot.IDManager.Groups) == 0 {
		return bot.Templates.Messages.GroupListEmpty
	}

	var result strings.Builder
	result.WriteString(bot.Templates.Messages.GroupListTitle)

	for i, group := range bot.IDManager.Groups {
		result.WriteString(fmt.Sprintf(bot.Templates.Messages.GroupListItem, i+1, group.Name, group.ID, group.FirstContact))
		result.WriteString("\n\n")
	}

	return result.String()
}

// Start 启动机器人
func (bot *DingTalkBot) Start(ctx context.Context) error {
	logger.SetLogger(logger.NewStdTestLogger())

	// 创建Stream客户端
	bot.Client = client.NewStreamClient(
		client.WithAppCredential(
			client.NewAppCredentialConfig(bot.Config.ClientID, bot.Config.ClientSecret),
		),
	)

	// 注册消息回调
	bot.Client.RegisterChatBotCallbackRouter(bot.OnChatBotMessageReceived)

	log.Println(bot.Templates.Messages.System.BotStarting)

	// 启动客户端
	if err := bot.Client.Start(ctx); err != nil {
		return fmt.Errorf(bot.Templates.Messages.System.BotStartFailed, err)
	}

	log.Println(bot.Templates.Messages.System.BotStarted)
	return nil
}

// Stop 停止机器人
func (bot *DingTalkBot) Stop() {
	if bot.Client != nil {
		bot.Client.Close()
		log.Println(bot.Templates.Messages.System.BotStopped)
	}
}
