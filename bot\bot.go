package bot

import (
	"context"
	"fmt"
	"log"
	"strings"

	"shopifyordergo/config"

	"github.com/open-dingtalk/dingtalk-stream-sdk-go/chatbot"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/client"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/logger"
)

// DingTalkBot 钉钉机器人结构
type DingTalkBot struct {
	Config    *config.DingTalkConfig
	IDManager *config.IDManager
	Client    *client.StreamClient
}

// NewDingTalkBot 创建新的钉钉机器人实例
func NewDingTalkBot(cfg *config.DingTalkConfig, idManager *config.IDManager) *DingTalkBot {
	return &DingTalkBot{
		Config:    cfg,
		IDManager: idManager,
	}
}

// OnChatBotMessageReceived 处理接收到的机器人消息
func (bot *DingTalkBot) OnChatBotMessageReceived(ctx context.Context, data *chatbot.BotCallbackDataModel) ([]byte, error) {
	log.Printf("收到消息: %s", data.Text.Content)
	log.Printf("发送者ID: %s", data.SenderStaffId)
	log.Printf("发送者昵称: %s", data.SenderNick)
	log.Printf("会话ID: %s", data.ConversationId)
	log.Printf("会话标题: %s", data.ConversationTitle)
	log.Printf("会话类型: %s", data.ConversationType)
	log.Printf("@用户列表: %v", data.AtUsers)

	// 记录用户ID和群组ID
	bot.recordIDs(data)

	// 处理消息内容
	content := strings.TrimSpace(data.Text.Content)
	response := bot.processMessage(content, data)

	// 回复消息
	replier := chatbot.NewChatbotReplier()
	if err := replier.SimpleReplyText(ctx, data.SessionWebhook, []byte(response)); err != nil {
		log.Printf("回复消息失败: %v", err)
		return nil, err
	}

	return []byte(""), nil
}

// recordIDs 记录用户ID和群组ID
func (bot *DingTalkBot) recordIDs(data *chatbot.BotCallbackDataModel) {
	var updated bool

	// 记录用户ID
	if data.SenderStaffId != "" {
		if bot.IDManager.AddUser(data.SenderStaffId, data.SenderNick) {
			log.Printf("新用户ID已记录: %s (%s)", data.SenderStaffId, data.SenderNick)
			updated = true
		}
	}

	// 判断是否为群聊，只有群聊才记录群组ID
	if bot.isGroupChat(data) {
		groupName := "未知群组"
		if data.ConversationTitle != "" {
			groupName = data.ConversationTitle
		}
		if bot.IDManager.AddGroup(data.ConversationId, groupName) {
			log.Printf("新群组ID已记录: %s (%s)", data.ConversationId, groupName)
			updated = true
		}
	} else {
		log.Printf("私聊消息，不记录群组ID")
	}

	// 如果有更新，保存到文件
	if updated {
		if err := bot.IDManager.SaveIDManager("dingdingid.yaml"); err != nil {
			log.Printf("保存ID管理文件失败: %v", err)
		} else {
			log.Println("ID管理文件已更新")
		}
	}
}

// isGroupChat 判断是否为群聊
func (bot *DingTalkBot) isGroupChat(data *chatbot.BotCallbackDataModel) bool {
	// 方法1: 检查会话类型
	if data.ConversationType == "2" {
		return true // 群聊
	}
	if data.ConversationType == "1" {
		return false // 私聊
	}

	// 方法2: 检查会话标题（群聊通常有标题，私聊没有）
	if data.ConversationTitle != "" {
		return true
	}

	// 方法3: 检查ConversationId格式（这是备用方法，不太可靠）
	// 群聊ID通常以特定格式开头，但这个规则可能会变化
	// 记录调试信息以便分析
	log.Printf("无法确定聊天类型，ConversationType: %s, ConversationTitle: %s, ConversationId: %s",
		data.ConversationType, data.ConversationTitle, data.ConversationId)

	// 如果以上方法都无法确定，默认认为是私聊（更安全的做法）
	return false
}

// processMessage 处理消息内容并生成回复
func (bot *DingTalkBot) processMessage(content string, data *chatbot.BotCallbackDataModel) string {
	// 移除@机器人的部分
	content = strings.TrimSpace(content)

	// 基本命令处理
	switch {
	case strings.Contains(content, "帮助") || strings.Contains(content, "help"):
		return bot.getHelpMessage()
	case strings.Contains(content, "状态") || strings.Contains(content, "status"):
		return bot.getStatusMessage()
	case strings.Contains(content, "用户列表"):
		return bot.getUserListMessage()
	case strings.Contains(content, "群组列表"):
		return bot.getGroupListMessage()
	default:
		return fmt.Sprintf("收到消息: %s\n\n发送 '帮助' 查看可用命令", content)
	}
}

// getHelpMessage 获取帮助信息
func (bot *DingTalkBot) getHelpMessage() string {
	return `🤖 钉钉机器人帮助信息

可用命令：
• 帮助/help - 显示此帮助信息
• 状态/status - 显示机器人状态
• 用户列表 - 显示已记录的用户ID
• 群组列表 - 显示已记录的群组ID

机器人功能：
✅ 自动记录用户ID和群组ID
✅ 支持@机器人对话
✅ 支持主动推送消息（需要先与机器人对话）

使用说明：
1. 在群组中@机器人或私聊机器人
2. 机器人会自动记录您的ID
3. 管理员可以使用记录的ID主动推送消息`
}

// getStatusMessage 获取状态信息
func (bot *DingTalkBot) getStatusMessage() string {
	userCount := len(bot.IDManager.Users)
	groupCount := len(bot.IDManager.Groups)

	return fmt.Sprintf(`🔍 机器人状态信息

📊 统计数据：
• 已记录用户数: %d
• 已记录群组数: %d

⚙️ 配置信息：
• App ID: %s
• Agent ID: %s
• Client ID: %s

✅ 机器人运行正常`, userCount, groupCount, bot.Config.AppID, bot.Config.AgentID, bot.Config.ClientID)
}

// getUserListMessage 获取用户列表信息
func (bot *DingTalkBot) getUserListMessage() string {
	if len(bot.IDManager.Users) == 0 {
		return "📝 暂无已记录的用户"
	}

	var result strings.Builder
	result.WriteString("👥 已记录用户列表：\n\n")

	for i, user := range bot.IDManager.Users {
		result.WriteString(fmt.Sprintf("%d. %s\n", i+1, user.Name))
		result.WriteString(fmt.Sprintf("   ID: %s\n", user.ID))
		result.WriteString(fmt.Sprintf("   首次联系: %s\n\n", user.FirstContact))
	}

	return result.String()
}

// getGroupListMessage 获取群组列表信息
func (bot *DingTalkBot) getGroupListMessage() string {
	if len(bot.IDManager.Groups) == 0 {
		return "📝 暂无已记录的群组"
	}

	var result strings.Builder
	result.WriteString("🏢 已记录群组列表：\n\n")

	for i, group := range bot.IDManager.Groups {
		result.WriteString(fmt.Sprintf("%d. %s\n", i+1, group.Name))
		result.WriteString(fmt.Sprintf("   ID: %s\n", group.ID))
		result.WriteString(fmt.Sprintf("   首次联系: %s\n\n", group.FirstContact))
	}

	return result.String()
}

// Start 启动机器人
func (bot *DingTalkBot) Start(ctx context.Context) error {
	logger.SetLogger(logger.NewStdTestLogger())

	// 创建Stream客户端
	bot.Client = client.NewStreamClient(
		client.WithAppCredential(
			client.NewAppCredentialConfig(bot.Config.ClientID, bot.Config.ClientSecret),
		),
	)

	// 注册消息回调
	bot.Client.RegisterChatBotCallbackRouter(bot.OnChatBotMessageReceived)

	log.Println("正在启动钉钉机器人...")

	// 启动客户端
	if err := bot.Client.Start(ctx); err != nil {
		return fmt.Errorf("启动机器人失败: %v", err)
	}

	log.Println("钉钉机器人启动成功！")
	return nil
}

// Stop 停止机器人
func (bot *DingTalkBot) Stop() {
	if bot.Client != nil {
		bot.Client.Close()
		log.Println("钉钉机器人已停止")
	}
}
