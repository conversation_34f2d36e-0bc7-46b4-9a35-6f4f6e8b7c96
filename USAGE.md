# 钉钉机器人使用指南

## 快速开始

### 1. 配置环境
1. 复制 `.env.example` 为 `.env`
2. 填入您的钉钉机器人配置信息：
   ```
   app id:您的应用ID
   AgentId:您的AgentID  
   Client ID:您的ClientID
   Client Secret:您的ClientSecret
   ```

### 2. 启动机器人
```bash
# 编译程序
go build -o dingtalk-bot.exe

# 运行程序
./dingtalk-bot.exe
```

### 3. 与机器人对话
1. 将机器人添加到钉钉群组
2. 在群组中@机器人，或与机器人私聊
3. 机器人会自动记录您的用户ID和群组ID

## 机器人命令

### 对话命令（在钉钉中使用）
- `@机器人 帮助` - 显示帮助信息
- `@机器人 状态` - 显示机器人状态
- `@机器人 用户列表` - 显示已记录用户
- `@机器人 群组列表` - 显示已记录群组

### 命令行界面（在程序控制台中使用）

#### 基本命令
```bash
help                    # 显示帮助
status                  # 显示状态
users                   # 显示用户列表
groups                  # 显示群组列表
reload                  # 重新加载ID文件
exit                    # 退出程序
```

#### 发送消息
```bash
# 向指定用户发送消息
send user 123456789 Hello World

# 向指定群组发送消息
send group cid123456789 群组通知

# 向所有用户广播消息
broadcast users 系统维护通知

# 向所有群组广播消息  
broadcast groups 重要公告
```

## ID管理

### 自动记录
- 当用户@机器人或私聊机器人时，系统会自动记录：
  - 用户ID (SenderStaffId)
  - 群组ID (ConversationId)
  - 首次联系时间

### 手动管理
您可以直接编辑 `dingdingid.yaml` 文件：

```yaml
users:
  - id: "123456789"
    name: "张三"
    first_contact: "2025-01-01 12:00:00"

groups:
  - id: "cid123456789"
    name: "开发团队群"
    first_contact: "2025-01-01 12:00:00"
```

## 使用场景

### 1. 系统通知
```bash
# 系统维护通知
broadcast users 系统将于今晚22:00-24:00进行维护，请提前保存工作

# 紧急通知
broadcast groups 【紧急】服务器异常，正在处理中...
```

### 2. 定向消息
```bash
# 向特定用户发送个人消息
send user 123456789 您的报告已审核通过

# 向特定群组发送消息
send group cid123456789 本周会议安排已更新
```

### 3. 状态查询
```bash
# 查看机器人状态
status

# 查看已记录的用户
users

# 查看已记录的群组
groups
```

## 注意事项

### 权限要求
1. 机器人需要有发送消息的权限
2. 用户必须先与机器人对话才能接收主动推送
3. 群组需要先添加机器人才能发送消息

### 频率限制
- 钉钉API有调用频率限制
- 批量发送时程序会自动添加延迟
- 建议合理控制消息发送频率

### 安全建议
1. 妥善保管 `.env` 文件中的敏感信息
2. 不要将配置文件提交到版本控制系统
3. 定期检查和清理 `dingdingid.yaml` 中的ID列表

## 故障排除

### 连接问题
- 检查网络连接
- 验证Client ID和Client Secret
- 确认机器人应用状态正常

### 消息发送失败
- 确认目标用户/群组ID正确
- 检查机器人权限设置
- 查看程序日志输出

### ID记录问题
- 确认用户已与机器人对话
- 检查 `dingdingid.yaml` 文件格式
- 使用 `reload` 命令重新加载ID文件

## 扩展开发

### 添加新命令
在 `bot/bot.go` 的 `processMessage` 函数中添加新的命令处理逻辑。

### 集成其他系统
可以在消息处理函数中添加与其他系统的集成代码，如：
- 数据库查询
- API调用
- 文件操作

### 定时任务
可以添加定时任务功能，定期发送通知或执行特定操作。
